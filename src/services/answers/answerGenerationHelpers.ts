import OpenAI from "openai";
import { logger } from "@/services/logger";
import { config } from "@/config";
import { SystemPrompt } from "@/prompts/system_prompts";
import { QuestionPrompt } from "@/prompts/question_prompt";
import { teleprompterPrompt } from "@/prompts/teleprompter";
import { Site } from "@/store/models/site";

/**
 * Generates answer using OpenAI
 */
export const getAnswer = async (
  question: string,
  context: string,
  clientId: number = 211,
  overrideTexts: string[] = [],
  internalType: string | null = null,
  isTeleprompter: boolean = false,
) => {
  const openai = new OpenAI();

  if (overrideTexts.length > 0) {
    logger.info(`Applying ${overrideTexts.length} overrides for question: "${question}"`);
    logger.info(`Override texts: ${JSON.stringify(overrideTexts)}`);
  } else {
    logger.info(`No overrides found for question: "${question}"`);
  }

  let cityName = "";
  let siteUrl = "";
  let siteSystemPrompt = SystemPrompt;
  let siteUserPrompt = QuestionPrompt;

  if (isTeleprompter) {
    siteSystemPrompt = teleprompterPrompt;
  } else if (internalType === "true") {
    const { SystemPrompt: InternalSystemPrompt } = await import("@/prompts/internal/system_prompts");
    const { QuestionPrompt: InternalQuestionPrompt } = await import("@/prompts/internal/question_prompt");

    siteSystemPrompt = InternalSystemPrompt;
    siteUserPrompt = InternalQuestionPrompt;

    logger.info(`Using internal prompts for question: "${question}"`);
  }

  try {
    const site = await Site.findOne({ where: { clientId, enabled: true } });
    if (site) {
      siteUrl = site.getDataValue("url");
    }
  } catch (error) {
    logger.warn(`Error fetching site info for clientId ${clientId}:`, error);
  }

  logger.info(`Final system prompt: ${siteSystemPrompt.substring(0, 200)}...`);
  logger.info(`Final user prompt: ${siteUserPrompt.substring(0, 200)}...`);

  const response = await openai.chat.completions.create({
    model: config.completionsModel,
    messages: [
      {
        role: "system",
        content: siteSystemPrompt,
      },
      {
        role: "user",
        content: siteUserPrompt,
      },
    ],
    // temperature: internalType === "internal" ? 0.1 : config.temperature,
    // max_tokens: 4000,
    max_completion_tokens: 4000,
  });

  const answer = response.choices[0].message.content || "";
  const tokenUsage = response.usage?.total_tokens || 0;

  return { answer, tokenUsage, siteSystemPrompt, siteUserPrompt };
};