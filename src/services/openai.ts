/* eslint-disable @typescript-eslint/no-floating-promises */
/* eslint-disable @typescript-eslint/no-unsafe-assignment */
import path from "path";
import fs from "fs";

import OpenAI from "openai";


import { logger, trackProgress } from "@/services/logger";
import { getVectorStore } from "@/services/embeddings/create_embeddings";
import { config } from "@/config";
import { QuestionEmbedding } from "@/store/models/question_embedding";
import { SystemPrompt } from "@/prompts/system_prompts";
import { QuestionPrompt } from "@/prompts/question_prompt";
import { Site } from "@/store/models/site";
import {
  extractSourcesFromResults,
  generateSourceSummariesBatch,
  prepareSourcesForSummarization,
  applySummariesToSources,
  saveSummariesToSourceLinks,
  checkForSourceOverrides,
  logSourceDateInfo,
  extractPublicationDate,
} from "@/utils/sourceHelpers";
import { sequelize } from "@/store/instance";
import { teleprompterPrompt } from "../prompts/teleprompter";
import { videoAnswerSelectionPrompt } from "@/prompts/video_answer_selection";
import { findMatchingAnswers } from "./answers/videoAnswerHelpers";

const MAX_CONTEXT_LENGTH = 100000;
const MAX_ARTICLES = 4;

// Configuration for video answer matching
const MAX_KEYWORD_CANDIDATES = 5; // Number of top keyword matches to send to OpenAI for evaluation

// Stop words to filter out when analyzing word overlap
const STOP_WORDS = [
  "the",
  "and",
  "or",
  "but",
  "in",
  "on",
  "at",
  "to",
  "for",
  "of",
  "with",
  "by",
  "about",
  "as",
  "what",
  "is",
  "are",
  "where",
  "when",
  "how",
  "why",
  "can",
  "do",
  "does",
  "which",
  "who",
  "whom",
  "whose",
  "a",
  "an",
  "was",
  "were",
  "be",
  "been",
  "have",
  "has",
  "had",
  "will",
  "would",
  "could",
  "should",
  "this",
  "that",
  "these",
  "those",
  "there",
  "here",
  "then",
  "than",
  "from",
  "into",
  "out",
  "up",
  "down",
];

const CLIENT_DEFAULTs: Record<string, string> = {
  "299": "For immediate assistance, please contact Human Resources at 843-918-1114."
};

const tempDir = path.join(process.cwd(), "temp");

if (!fs.existsSync(tempDir)) {
  fs.mkdirSync(tempDir, { recursive: true });
}

const truncateContext = (context: string) => {
  if (context.length <= MAX_CONTEXT_LENGTH) return context;

  const truncated = context.slice(0, MAX_CONTEXT_LENGTH);
  const lastSpace = truncated.lastIndexOf(" ");
  return truncated.slice(0, lastSpace) + "...";
};

// Default: Arlington
const getAnswer = async (
  question: string,
  context: string,
  clientId: number = 211,
  overrideTexts: string[] = [],
  internalType: string | null = null,
  isTeleprompter: boolean = false,
) => {
  const openai = new OpenAI();

  // Log if overrides are being applied
  if (overrideTexts.length > 0) {
    logger.info(`Applying ${overrideTexts.length} overrides for question: "${question}"`);
    logger.info(`Override texts: ${JSON.stringify(overrideTexts)}`);
  } else {
    logger.info(`No overrides found for question: "${question}"`);
  }

  // Default values
  let cityName = "";
  let siteUrl = "";
  let siteSystemPrompt = SystemPrompt;
  let siteUserPrompt = QuestionPrompt;

  // If teleprompter is requested, use teleprompter prompt
  if (isTeleprompter) {
    // Import teleprompter prompt
    siteSystemPrompt = teleprompterPrompt;
  }
  // If internalType is specified, use internal prompts
  else if (internalType === "true") {
    // Import internal prompts
    const { SystemPrompt: InternalSystemPrompt } = await import("@/prompts/internal/system_prompts");
    const { QuestionPrompt: InternalQuestionPrompt } = await import("@/prompts/internal/question_prompt");

    siteSystemPrompt = InternalSystemPrompt;
    siteUserPrompt = InternalQuestionPrompt;

    logger.info(`Using internal prompts for question: "${question}"`);
  }

  // Try to get site info for this client
  try {
    const sites = await Site.findAll({
      where: {
        clientId,
        enabled: true,
      },
    });

    const site = sites[0];

    cityName = site.getDataValue("name") || "City";
    siteUrl = site.getDataValue("url");

    // Add CLIENT_DEFAULTs
    const clientDefaultText = CLIENT_DEFAULTs[clientId.toString()];
    if (clientDefaultText) {
      siteSystemPrompt = `${clientDefaultText}\n`;
      siteUserPrompt = `${clientDefaultText}\n`;
      logger.info(`Added client default text for clientId ${clientId}: ${clientDefaultText}`);
    }

    // Create a customized prompt for each site
    if (overrideTexts.length > 0) {
      // Format overrides to be more explicit and override the default behavior
      const formattedOverrides = overrideTexts
        .map(
          (text) =>
            `IMPORTANT: The following information is current and up-to-date. Use this information to answer the question: ${text}`,
        )
        .join("\n\n");

      // Add overrides to the beginning of the system prompt
      siteSystemPrompt =
        `OVERRIDE NOTICE: The information below takes precedence over any general guidelines about recency or dates.\n\n${formattedOverrides}\n\n${siteSystemPrompt}`
          .replace("CITY_NAME", cityName)
          .replace("CONTEXT", context)
          .replace("QUESTION", question)
          .replace("LINK", siteUrl);

      // For the user prompt, we'll make the override even more explicit
      siteUserPrompt =
        `IMPORTANT: Use the override information provided in the system message to answer this question about ${question}. This information is current regardless of its date.\n\n${siteUserPrompt}`
          .replace("CITY_NAME", cityName)
          .replace("CONTEXT", context)
          .replace("QUESTION", question)
          .replace("LINK", siteUrl);
    } else {
      // Original prompt formatting without overrides
      siteSystemPrompt = siteSystemPrompt
        .replace("CITY_NAME", cityName)
        .replace("CONTEXT", context)
        .replace("QUESTION", question)
        .replace("LINK", siteUrl);

      siteUserPrompt = siteUserPrompt
        .replace("CITY_NAME", cityName)
        .replace("CONTEXT", context)
        .replace("QUESTION", question)
        .replace("LINK", siteUrl);
    }
  } catch (error) {
    logger.warn(`Error fetching site info for clientId ${clientId}:`, error);
  }

  // Log the final prompts being sent to OpenAI
  logger.info(`Final system prompt: ${siteSystemPrompt.substring(0, 200)}...`);
  logger.info(`Final user prompt: ${siteUserPrompt.substring(0, 200)}...`);

  const response = await openai.chat.completions.create({
    model: config.completionsModel,
    messages: [
      {
        role: "system",
        content: siteSystemPrompt,
      },
      {
        role: "user",
        content: siteUserPrompt,
      },
    ],
    // temperature: internalType === "internal" ? 0.1 : config.temperature,
    // max_tokens: 3000,
    max_completion_tokens: 3000,
  });

  const answer = response.choices[0].message.content || "";
  const tokenUsage = response.usage?.total_tokens || 0;

  return { answer, tokenUsage, siteSystemPrompt, siteUserPrompt };
};

const getQuestionEmbedding = async (question: string, clientId: number) => {
  const openai = new OpenAI();

  // Transform the question for better vector search
  const transformedQuestion = await transformQuestionForVectorSearch(question, clientId);

  // Log for debugging
  logger.info(`Original question: "${question}"`);
  logger.info(`Transformed question: "${transformedQuestion}"`);

  const response = await openai.embeddings.create({
    model: config.embeddingModel,
    input: question,
  });
  return { embedding: response.data[0].embedding, tokenUsage: response.usage.total_tokens || 0 };
};

/**
 * Transforms a user question to optimize for vector search
 * Uses OpenAI to create a more searchable version of the question
 */
const transformQuestionForVectorSearch = async (question: string, clientId: number): Promise<string> => {
  // Convert to lowercase and tokenize
  const words = question.split(/\s+/);

  // Extract important keywords (words longer than 4 chars and capitalized acronyms)
  const keywords = words.filter((word) => {
    const cleanWord = word.replace(/[^\w]/g, "");
    // Check for capitalized acronyms (all caps words of 2+ characters)
    const isAcronym = cleanWord.length >= 2 && cleanWord === cleanWord.toUpperCase();
    // Check for important words (longer than 4 chars, not in stop words)
    const isImportantWord = cleanWord.length > 4 && !STOP_WORDS.includes(cleanWord.toLowerCase());

    return isAcronym || isImportantWord;
  });

  // Filter out question words from the beginning of the query
  let contentWords = words;
  if (STOP_WORDS.includes(words[0].toLowerCase())) {
    // If query starts with a question word, remove it and possibly the next word if it's "is", "are", etc.
    contentWords = words.slice(1);
    if (
      contentWords.length > 0 &&
      ["is", "are", "was", "were", "the", "a", "an"].includes(contentWords[0].toLowerCase())
    ) {
      contentWords = contentWords.slice(1);
    }
  }

  // Join the remaining content words
  const strippedQuery = contentWords.join(" ");

  // Get city name for context
  let cityName = "";
  try {
    const site = await Site.findOne({ where: { clientId, enabled: true } });
    if (site) {
      cityName = site.getDataValue("name") || "";
    }
  } catch (error) {
    logger.warn(`Error fetching site info for clientId ${clientId}:`, error);
  }

  // Combine stripped query with boosted keywords
  // Add keywords with a boost by repeating them
  const keywordsBoost = keywords.length > 0 ? ` ${keywords.join(" ")} ${keywords.join(" ")}` : "";

  // Log the transformation for debugging
  logger.info(`Original question: "${question}"`);
  logger.info(`Stripped query: "${strippedQuery}"`);
  logger.info(`Extracted keywords: ${JSON.stringify(keywords)}`);
  logger.info(`Final search query: "${strippedQuery}${keywordsBoost}"`);

  return `QUESTION: ${strippedQuery} KEYWORDS: ${keywordsBoost}`;
};

interface AnswerResult {
  answer: string;
  answerTokenUsage: number | undefined;
  searchTime: number;
  answeringTime: number;
  sources: Array<{
    url: string;
    score: number;
  }>;
  matchingAnswers: any[]; // Add this line to include matching answers in the interface
}

/**
 * Calculates word overlap between question and transcription text
 * Returns a score between 0 and 1 representing the percentage of question words found in transcription
 */
const calculateWordOverlap = (question: string, transcription: string): number => {
  if (!question || !transcription) return 0;

  // Normalize and extract meaningful words from question
  const questionWords = question
    .toLowerCase()
    .replace(/[^\w\s]/g, " ")
    .split(/\s+/)
    .filter((word) => word.length > 2) // Filter out very short words
    .filter((word) => !STOP_WORDS.includes(word));

  if (questionWords.length === 0) return 0;

  // Normalize transcription text
  const transcriptionText = transcription.toLowerCase().replace(/[^\w\s]/g, " ");

  // Count how many question words appear in transcription
  const matchingWords = questionWords.filter((word) => transcriptionText.includes(word));

  const overlapScore = matchingWords.length / questionWords.length;

  // logger.info(`Word overlap analysis:`);
  // logger.info(`Question words: ${JSON.stringify(questionWords)}`);
  // logger.info(`Matching words: ${JSON.stringify(matchingWords)}`);
  // logger.info(`Overlap score: ${overlapScore.toFixed(2)} (${matchingWords.length}/${questionWords.length})`);

  return overlapScore;
};

/**
 * Uses OpenAI to select the most relevant answer from candidates
 */
const selectBestAnswerWithAI = async (question: string, candidateAnswers: any[]): Promise<any | null> => {
  if (candidateAnswers.length === 0) {
    return null;
  }

  try {
    const openai = new OpenAI();

    // Prepare the candidate answers for OpenAI evaluation
    const candidatesText = candidateAnswers
      .map((answer, index) => {
        return `Answer ${index + 1}:
Title: ${answer.question || "No title"}
Transcript: ${answer.transcription || "No transcript"}
Answer ID: ${answer.repd_answer_id}
---`;
      })
      .join("\n\n");

    const prompt = videoAnswerSelectionPrompt(question, candidatesText);

    const response = await openai.chat.completions.create({
      model: config.completionsModel,
      messages: [
        {
          role: "user",
          content: prompt,
        },
      ],
      // temperature: 0.1,
      // max_tokens: 50,
      max_completion_tokens: 50,
    });

    const aiResponse = response.choices[0].message.content?.trim();

    if (!aiResponse || aiResponse === "NONE") {
      logger.info(`OpenAI determined no relevant answers for question: "${question}"`);
      return null;
    }

    // Find the selected answer by ID
    const selectedAnswerId = aiResponse;
    const selectedAnswer = candidateAnswers.find((answer) => answer.repd_answer_id?.toString() === selectedAnswerId);

    if (selectedAnswer) {
      logger.info(`OpenAI selected answer ${selectedAnswerId} for question: "${question}"`);
      return selectedAnswer;
    } else {
      logger.warn(`OpenAI selected answer ID ${selectedAnswerId} but it wasn't found in candidates`);
      return null;
    }
  } catch (error) {
    logger.error("Error using OpenAI to select best answer:", error);
    // Fallback to the first candidate if AI selection fails
    return candidateAnswers[0] || null;
  }
};

/**
 * Converts transcription items to plain text
 */
const transcriptionToText = (transcription: any): string => {
  if (!transcription || !transcription.items) {
    return "";
  }

  return transcription.items
    .map((item: any) => {
      if (item.alternatives && item.alternatives.length > 0) {
        return item.alternatives[0].content;
      }
      return "";
    })
    .join(" ")
    .trim();
};



const answerUserQuestion = async (
  question: string,
  clientId: number,
  internalType: string | null = null,
  isTeleprompter: boolean = false,
  userId: number | null = null,
): Promise<AnswerResult> => {
  try {
    const vectorStore = await getVectorStore();

    // Get site URL for this client
    let siteUrl = "";
    try {
      const site = await Site.findOne({ where: { clientId, enabled: true } });
      if (site) {
        siteUrl = site.getDataValue("url");
      } else {
        logger.warn(`No enabled site found for clientId ${clientId}`);
      }
    } catch (error) {
      logger.warn(`Error fetching site info for clientId ${clientId}:`, error);
    }

    const stopTrackingGetQuestionEmbedding = trackProgress("Getting question embedding");

    const { embedding: questionEmbedding, tokenUsage: questionEmbeddingTokenUsage } = await getQuestionEmbedding(
      question,
      clientId,
    );
    const getQuestionEmbeddingTime = stopTrackingGetQuestionEmbedding();

    // Find matching answers based on keyword matching
    // Now using the same data source as the frontend (answer_embeddings table)
    let matchingAnswers: any[] = [];

    try {
      matchingAnswers = await findMatchingAnswers(question, clientId);
      logger.info(`Found ${matchingAnswers.length} matching answers for question: "${question}"`);
    } catch (error) {
      logger.error("Error finding matching answers:", error);
      matchingAnswers = [];
    }

    const stopTrackingSearchTime = trackProgress("Searching for relevant documents in vector store");

    // Get client connection from the pool
    const vectorDbClient = await vectorStore.pool.connect();

    // Format the embedding vector for PostgreSQL
    const formattedEmbedding = `[${questionEmbedding.join(",")}]`;

    // Determine schema name based on client ID
    const schema = `client_${clientId}`;
    let query = "";
    const queryParams = [formattedEmbedding];

    // Extract keywords for additional filtering
    const extractKeywordsFromQuestion = (question: string): string[] => {
      // Convert to lowercase and tokenize
      const words = question.split(/\s+/);

      // Extract important keywords (words longer than 4 chars and capitalized acronyms)
      return words.filter((word) => {
        const cleanWord = word.replace(/[^\w]/g, "");
        // Check for capitalized acronyms (all caps words of 2+ characters)
        const isAcronym = cleanWord.length >= 2 && cleanWord === cleanWord.toUpperCase();
        // Check for important words (longer than 4 chars, not in stop words)
        const isImportantWord = cleanWord.length > 4 && !STOP_WORDS.includes(cleanWord.toLowerCase());

        return isAcronym || isImportantWord;
      });
    };

    // Now we can safely use it
    const keywords = extractKeywordsFromQuestion(question);

    // Fallback to original query if no client ID is found
    query = `
      WITH ranked_results AS (
        SELECT
          content,
          metadata,
          embedding <#> $1::vector AS semantic_score,
          CASE
            WHEN metadata->>'publicationDate' ~ '^\d{4}-\d{2}-\d{2}' THEN
              EXTRACT(EPOCH FROM NOW() - (metadata->>'publicationDate')::timestamp)
            WHEN metadata->>'lastModified' ~ '^\d{4}-\d{2}-\d{2}' THEN
              EXTRACT(EPOCH FROM NOW() - (metadata->>'lastModified')::timestamp)
            WHEN metadata->>'createdAt' ~ '^\d{4}-\d{2}-\d{2}' THEN
              EXTRACT(EPOCH FROM NOW() - (metadata->>'createdAt')::timestamp)
            WHEN metadata->>'extractedDate' ~ '^\d{4}-\d{2}-\d{2}' THEN
              EXTRACT(EPOCH FROM NOW() - (metadata->>'extractedDate')::timestamp)
            ELSE 63072000 -- Default to 2 years old if no date information
            -- NOW() - INTERVAL '2 years' -- fallback
          END AS age_seconds,
          -- Add keyword matching score (0 if no match, 1 if match)
          CASE
            ${(Array.isArray(keywords) ? keywords : []).map((k: string) => `WHEN content ILIKE '%${k}%' THEN 1`).join("\n            ")}
            WHEN content LIKE 'NO CONTENT' THEN 1
            ELSE 0
          END AS keyword_match
        FROM ${schema}.mv_embeddings
      )
      SELECT
        content,
        metadata,
        semantic_score,
        age_seconds,
        keyword_match,
        -- Adjust combined score to prioritize keyword matches
        semantic_score + (age_seconds / 31536000.0) * 0.5 AS combined_score
      FROM ranked_results
      WHERE semantic_score < ${internalType === "true" ? 50 : 0.15} OR keyword_match >= 1
      ORDER BY combined_score ASC
      LIMIT ${MAX_ARTICLES}
    `;

    // Execute the query with proper string interpolation for materialized view names
    const preparedQuery = query.replace(/\$\{clientId\}/g, clientId.toString() || "");
    const results = await vectorDbClient.query(preparedQuery, queryParams);
    vectorDbClient.release();

    // Check if we have results
    if (!results.rows || results.rows.length === 0) {
      logger.warn(`No relevant documents found for question: "${question}" and clientId: ${clientId}`);
      logger.warn(
        `Query executed: ${preparedQuery.replace(/\$1/g, formattedEmbedding).replace(/\$2/g, clientId.toString())}`,
      );

      // Store the question and answer
      try {
        QuestionEmbedding.create({
          question,
          answer: "I'm sorry, I couldn't find any relevant information to answer your question.",
          embedding: questionEmbedding,
          relatedEmbeddings: "[]",
          questionEmbeddingTokenUsage,
          answerTokenUsage: 0,
          searchTime: 0,
          getQuestionEmbeddingTime,
          answeringTime: 0,
          clientId,
          sources: [
            ...(internalType === "true" ? [{ text: "Internal question", type: "internal" }] : []),
            ...results.rows.map((row) => ({ text: row.content, type: "source" })),
          ],
        });
      } catch (error) {
        // Ignore errors when storing the question embedding
        logger.warn("Failed to store question embedding:", error);
      }

      return {
        answer: "I'm sorry, I couldn't find any relevant information to answer your question.",
        answerTokenUsage: 0,
        searchTime: 0,
        answeringTime: 0,
        sources: [],
        matchingAnswers: [],
      };
    }

    // Add this function to verify if content is recent enough to be presented as current
    const isContentRecent = (
      metadata: Record<string, any>,
      content: string,
    ): { isRecent: boolean; date: string | null } => {
      // Get current date and calculate cutoff date (1 year ago)
      const currentDate = new Date();
      const oneYearAgo = new Date();
      oneYearAgo.setFullYear(currentDate.getFullYear() - 1);

      // Make the check more strict - 6 months instead of 1 year
      const sixMonthsAgo = new Date();
      sixMonthsAgo.setMonth(currentDate.getMonth() - 6);

      // Try to get date from metadata
      let contentDate: Date | null = null;
      let dateString: string | null = null;

      // Check publication date first (most accurate)
      if (metadata.publicationDate) {
        try {
          contentDate = new Date(metadata.publicationDate);
          if (!isNaN(contentDate.getTime())) {
            dateString = metadata.publicationDate;
          }
        } catch (e) {
          contentDate = null;
        }
      }

      if (!contentDate && metadata.lastModified) {
        try {
          contentDate = new Date(metadata.lastModified);
          if (!isNaN(contentDate.getTime())) {
            dateString = metadata.lastModified;
          }
        } catch (e) {
          contentDate = null;
        }
      }

      if (!contentDate && metadata.createdAt) {
        // Only use createdAt if it's not a recent database entry for old content
        try {
          const createdDate = new Date(metadata.createdAt);
          if (!isNaN(createdDate.getTime())) {
            // If created recently but content seems old based on text, don't use createdAt
            const extractedDate = extractPublicationDate(metadata.sourceUrl || "", content, metadata);
            if (extractedDate && Math.abs(createdDate.getTime() - extractedDate.getTime()) > 30 * 24 * 60 * 60 * 1000) {
              // If extracted date is more than 30 days different from createdAt, use extracted date
              contentDate = extractedDate;
              dateString = extractedDate.toISOString();
            } else {
              contentDate = createdDate;
              dateString = metadata.createdAt;
            }
          }
        } catch (e) {
          contentDate = null;
        }
      }

      if (!contentDate && metadata.extractedDate) {
        try {
          contentDate = new Date(metadata.extractedDate);
          if (!isNaN(contentDate.getTime())) {
            dateString = metadata.extractedDate;
          }
        } catch (e) {
          contentDate = null;
        }
      }

      // Try to extract date from content and URL only if no valid metadata date found
      if (!contentDate) {
        try {
          const extractedDate = extractPublicationDate(metadata.sourceUrl || "", content, metadata);
          if (extractedDate && !isNaN(extractedDate.getTime())) {
            contentDate = extractedDate;
            dateString = extractedDate.toISOString();
          }
        } catch (e) {
          // Ignore extraction errors
        }
      }

      // For internal clients with limited documents, be more lenient
      // If we couldn't determine a date, don't mark as outdated automatically
      if (!contentDate || isNaN(contentDate.getTime())) {
        return { isRecent: true, date: null }; // Changed from false to true
      }

      // Check if the content date is within the last 6 months
      return {
        isRecent: contentDate >= sixMonthsAgo,
        date: dateString,
      };
    };

    // Build context with clear date information and verification
    let context = "";

    // Add a header for the most recent information
    if (results.rows.length > 0) {
      context += "MOST RECENT INFORMATION:\n";

      // Add date information for each result
      for (let i = 0; i < Math.min(2, results.rows.length); i++) {
        const row = results.rows[i];
        const metadata = row.metadata as Record<string, any>;

        // Verify if content is recent
        const { isRecent, date } = isContentRecent(metadata, row.content);

        // Try to get the date from metadata
        let dateInfo = "";
        if (date) dateInfo = `DATE: ${new Date(date).toLocaleDateString()}`;
        else if (metadata.lastModified) dateInfo = `DATE: ${new Date(metadata.lastModified).toLocaleDateString()}`;
        else if (metadata.createdAt) dateInfo = `DATE: ${new Date(metadata.createdAt).toLocaleDateString()}`;
        else if (metadata.extractedDate) dateInfo = `DATE: ${new Date(metadata.extractedDate).toLocaleDateString()}`;

        // Add recency flag to help the model identify outdated content
        if (!isRecent && i > 0) {
          dateInfo += ` [OUTDATED - DO NOT PRESENT AS CURRENT]`;
          context += `${dateInfo}\n${row.content}\n\n`;
        } else {
          context += `${dateInfo}\n${row.content}\n\n`;
        }
      }

      // Add older information if available
      if (results.rows.length > 2) {
        context += "OLDER INFORMATION [OUTDATED - DO NOT PRESENT AS CURRENT]:\n";
        for (let i = 2; i < results.rows.length; i++) {
          context += results.rows[i].content + "\n\n";
        }
      }
    }

    const searchTime = stopTrackingSearchTime();

    // Extract sources using the utility function
    const sourcesWithoutSummaries = extractSourcesFromResults(results.rows);

    // Add debug logging
    logSourceDateInfo(sourcesWithoutSummaries, results.rows);

    // Check for source overrides based on keywords in the question
    let overrideTexts: string[] = [];
    const overrideTextsPromise = checkForSourceOverrides(question, clientId)
      .then((texts) => {
        overrideTexts = texts;
      })
      .catch((error) => {
        logger.error("Error checking for source overrides in background:", error);
      });

    // Wait for overrides to be checked
    await overrideTextsPromise;

    // If we have overrides, use them directly instead of the regular context
    if (overrideTexts.length > 0) {
      // Replace the context with the override text
      context = overrideTexts.join("\n\n");

      // Add a clear marker at the beginning
      context = "CURRENT AND AUTHORITATIVE INFORMATION:\n\n" + context;

      // Log that we're using override text instead of regular context
      logger.info(`Using override text as context for question: "${question}"`);
    }

    // Start the answer generation with the potentially modified context
    const answerPromise = getAnswer(question, context, clientId, overrideTexts, internalType, isTeleprompter);

    // Start the answer generation - will need to wait for overrides before using them
    const stopTrackingAnswerTime = trackProgress("Generating answer");

    // Prepare sources for summarization
    const { contentsToSummarize, contentSourceMap } = prepareSourcesForSummarization(
      sourcesWithoutSummaries,
      results.rows,
    );

    // Generate summaries in batch
    const summariesPromise = generateSourceSummariesBatch(contentsToSummarize);

    // Wait for both operations to complete
    const [summaries, { answer, tokenUsage: answerTokenUsage, siteSystemPrompt, siteUserPrompt }] = await Promise.all([
      summariesPromise,
      answerPromise,
      overrideTextsPromise,
    ]);

    const answeringTime = stopTrackingAnswerTime();

    // Apply summaries to sources
    const sources = applySummariesToSources(sourcesWithoutSummaries, contentSourceMap, summaries);

    // Save summaries to source links in the background
    (() => {
      try {
        saveSummariesToSourceLinks(contentSourceMap, summaries);
      } catch (error) {
        logger.error("Error in background summary saving:", error);
      }
    })();

    // Store the question and answer
    try {
      QuestionEmbedding.create({
        question,
        answer,
        embedding: questionEmbedding,
        relatedEmbeddings: context,
        questionEmbeddingTokenUsage,
        answerTokenUsage,
        searchTime,
        getQuestionEmbeddingTime,
        answeringTime,
        clientId,
        userId: userId,
        sources: [
          ...(internalType === "true" ? [{ text: "Internal question", type: "internal" }] : []),
          ...overrideTexts.map((text) => ({ text, type: "override" })),
          ...sources,
          { systemPrompt: siteSystemPrompt, userPrompt: siteUserPrompt, matchingAnswers },
        ],
      });
    } catch (error) {
      // Ignore errors when storing the question embedding
      logger.warn("Failed to store question embedding:", error);
    }

    logger.info(`Answer: ${answer}`);
    logger.info(`Tokens used for answer: ${answerTokenUsage}`);
    logger.info(`Tokens used for question embedding: ${questionEmbeddingTokenUsage}`);
    // logger.info(`Sources: ${JSON.stringify(sources)}`);

    return {
      answer: answer.replace(/\*\*/g, "").replace(/```/g, ""),
      answerTokenUsage,
      searchTime,
      answeringTime,
      sources,
      matchingAnswers,
    };
  } catch (error) {
    logger.error(`Error in answerUserQuestion:`, error);
    return {
      answer: "I'm sorry, I encountered an error while processing your question.",
      answerTokenUsage: 0,
      searchTime: 0,
      answeringTime: 0,
      sources: [],
      matchingAnswers: [], // Add empty array for matching answers
    };
  }
};

export { answerUserQuestion, calculateWordOverlap };
